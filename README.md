# Maya文件管理器 - MVC架构版本

## 概述

这是一个使用MVC（Model-View-Controller）架构重构的Maya文件管理器工具。相比原版本，新版本具有更好的代码组织结构、更强的可扩展性和可复用性，支持在多个软件中使用。

## 主要特性

### 🏗️ MVC架构设计
- **Model层**: 负责数据管理和业务逻辑
- **View层**: 负责用户界面展示
- **Controller层**: 负责协调Model和View，处理用户交互

### 🔧 模块化组织
- 清晰的包结构，便于维护和扩展
- 松耦合设计，各模块职责明确
- 观察者模式，实现组件间通信

### ⚙️ 配置管理
- 支持JSON配置文件
- 便于多项目和多软件适配
- 运行时配置修改

### 🔄 多软件支持
- Maya (主要支持)
- 3ds Max (配置适配)
- Blender (配置适配)
- 易于扩展到其他软件

## 项目结构

```
maya_file_manager/
├── __init__.py                 # 主入口模块
├── models/                     # Model层
│   ├── __init__.py
│   └── file_manager_model.py   # 数据模型和业务逻辑
├── views/                      # View层
│   ├── __init__.py
│   └── file_manager_view.py    # UI界面组件
├── controllers/                # Controller层
│   ├── __init__.py
│   └── file_manager_controller.py # 控制器
└── config/                     # 配置管理
    ├── __init__.py
    └── config.py               # 配置管理类

config_examples/                # 配置示例
└── maya_project_config.json   # Maya项目配置示例

examples/                       # 使用示例
└── usage_examples.py          # 各种使用场景示例

maya_file_manager_mvc.py        # 快速启动脚本
save_load.py                   # 原版本文件（保留）
```

## 快速开始

### 基本使用

```python
# 方法1: 直接运行快速启动脚本
exec(open('maya_file_manager_mvc.py').read())

# 方法2: 导入模块使用
from maya_file_manager import show_manager
manager = show_manager()
```

### 自定义项目

```python
from maya_file_manager import create_maya_manager

# 创建自定义项目的文件管理器
manager = create_maya_manager(
    project_name="my_project",
    save_dir="D:/my_project/scenes/"
)
manager.start()
```

### 使用配置文件

```python
from maya_file_manager import FileManagerConfig, FileManagerApp

# 从配置文件加载
config = FileManagerConfig("config_examples/maya_project_config.json")
app = FileManagerApp(config)
app.start()
```

## 配置说明

### 配置文件格式

```json
{
    "save_dir": "D:/dev/Scgtest/0010/3d/mm/",
    "base_name": "dev_cgtest_0010_mm",
    "file_extension": ".ma",
    "file_suffix": "_mmq",
    "version_prefix": "v",
    "version_digits": 3,
    "future_versions_count": 5,
    "window_title": "Maya文件管理器",
    "software": "maya",
    "dockable": true
}
```

### 主要配置项

- `save_dir`: 文件保存目录
- `base_name`: 文件基础名称
- `file_extension`: 文件扩展名 (.ma, .max, .blend等)
- `version_prefix`: 版本号前缀 (默认"v")
- `version_digits`: 版本号位数 (默认3位，如v001)
- `future_versions_count`: 预置未来版本数量

## 功能特性

### 版本管理
- 自动版本号生成和排序
- 支持现有版本检测
- 预置未来版本选择
- 版本状态可视化（最新/历史/未创建）

### 文件操作
- 一键打开指定版本文件
- 保存到指定版本
- 覆盖确认机制
- 自动目录创建

### 用户界面
- Maya风格深色主题
- 可停靠窗口支持
- 可折叠日志区域
- 文件信息实时显示
- 点击打开文件夹功能

### 日志系统
- 操作记录追踪
- 多级别日志支持
- 实时日志显示
- 观察者模式通知

## API参考

### 主要类

#### FileManagerApp
应用程序主类，管理整个文件管理器的生命周期。

```python
app = FileManagerApp(config)
app.start()  # 启动
app.stop()   # 停止
app.is_running()  # 检查运行状态
```

#### FileManagerConfig
配置管理类，处理所有配置相关操作。

```python
config = FileManagerConfig("config.json")
config.get("save_dir")  # 获取配置
config.set("base_name", "new_project")  # 设置配置
config.save_to_file("new_config.json")  # 保存配置
```

#### FileManagerController
控制器类，协调Model和View。

```python
controller = FileManagerController(save_dir, base_name, config)
controller.show()  # 显示界面
controller.update_file_list()  # 更新文件列表
```

### 便捷函数

```python
# 快速启动
show_manager(project_name, save_dir)

# 关闭管理器
close_manager()

# 创建特定软件的管理器
create_maya_manager(project_name, save_dir)
create_max_manager(project_name, save_dir)
create_blender_manager(project_name, save_dir)
```

## 扩展开发

### 添加新软件支持

1. 在`config.py`中添加软件适配方法
2. 创建对应的文件操作类
3. 更新配置默认值

### 自定义UI主题

1. 修改`file_manager_view.py`中的样式定义
2. 在配置中添加主题选项
3. 实现主题切换逻辑

### 添加新功能

1. 在Model层添加业务逻辑
2. 在View层添加UI组件
3. 在Controller层添加交互处理

## 兼容性

- Maya 2022.5.1+
- Python 3.7+
- PySide2/PySide6
- Windows/macOS/Linux

## 更新日志

### v2.0.0
- 完全重构为MVC架构
- 添加配置管理系统
- 支持多软件适配
- 改进代码组织结构
- 添加观察者模式
- 增强错误处理

### v1.0.0
- 原始版本功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
