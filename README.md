# Maya文件管理器 - MVC架构版本

## 概述

这是一个使用MVC架构重构的Maya文件管理器工具，提供版本化文件管理功能。

## 主要特性

- **MVC架构**: 清晰的代码结构，易于维护和扩展
- **版本管理**: 自动版本号生成，支持历史版本管理
- **配置驱动**: 支持多项目配置，便于复用
- **Maya集成**: 原生Maya界面风格，支持停靠窗口

## 安装方法

### 自动安装（推荐）
运行 `install_to_maya.bat` 文件，脚本会自动将工具安装到Maya。

### 手动安装
1. 将 `maya_file_manager` 文件夹复制到Maya的scripts目录
2. 将 `maya_file_manager_mvc.py` 复制到Maya的scripts目录
3. 在Maya中运行：`exec(open('maya_file_manager_mvc.py').read())`

## 使用方法

### 基本使用
```python
# 快速启动
exec(open('maya_file_manager_mvc.py').read())

# 或者导入使用
from maya_file_manager import show_manager
manager = show_manager()
```

### 自定义项目
```python
from maya_file_manager import create_maya_manager
manager = create_maya_manager("my_project", "D:/my_project/scenes/")
manager.start()
```

## 功能说明

- **版本选择器**: 选择要操作的文件版本
- **Open按钮**: 打开选中的版本文件
- **Save按钮**: 保存当前场景到选中版本
- **信息显示**: 显示文件状态和路径信息
- **日志记录**: 可折叠的操作日志区域

## 配置

默认配置可以通过代码修改：
```python
from maya_file_manager import FileManagerConfig
config = FileManagerConfig()
config.set('save_dir', 'D:/your_project/scenes/')
config.set('base_name', 'your_project_name')
```

## 兼容性

- Maya 2022.5.1+
- Python 3.7+
- Windows/macOS/Linux
