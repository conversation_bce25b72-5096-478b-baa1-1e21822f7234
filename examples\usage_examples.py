# -*- coding: utf-8 -*-
"""
Maya文件管理器 - 使用示例
展示如何在不同场景下使用MVC架构的文件管理器
"""

# 示例1: 基本使用 - 使用默认配置
def example_basic_usage():
    """基本使用示例"""
    from maya_file_manager import show_manager, close_manager
    
    # 启动文件管理器
    manager = show_manager()
    
    # 使用完毕后关闭
    # close_manager()


# 示例2: 自定义项目配置
def example_custom_project():
    """自定义项目配置示例"""
    from maya_file_manager import create_maya_manager
    
    # 创建自定义项目的文件管理器
    manager = create_maya_manager(
        project_name="my_awesome_project",
        save_dir="D:/projects/my_awesome_project/scenes/"
    )
    
    # 启动
    if manager.start():
        print("项目文件管理器启动成功")
    
    # 获取配置信息
    config = manager.get_config()
    print(f"项目名称: {config.get('base_name')}")
    print(f"保存目录: {config.get('save_dir')}")


# 示例3: 使用配置文件
def example_with_config_file():
    """使用配置文件示例"""
    from maya_file_manager import FileManagerConfig, FileManagerApp
    
    # 从配置文件加载
    config = FileManagerConfig("config_examples/maya_project_config.json")
    
    # 创建应用
    app = FileManagerApp(config)
    
    # 启动
    app.start()


# 示例4: 多项目切换
def example_multi_project():
    """多项目切换示例"""
    from maya_file_manager import create_maya_manager, close_manager
    
    # 项目配置列表
    projects = [
        {"name": "project_A", "dir": "D:/projects/A/scenes/"},
        {"name": "project_B", "dir": "D:/projects/B/scenes/"},
        {"name": "project_C", "dir": "D:/projects/C/scenes/"},
    ]
    
    current_manager = None
    
    def switch_to_project(project_info):
        nonlocal current_manager
        
        # 关闭当前管理器
        if current_manager:
            current_manager.stop()
        
        # 创建新的管理器
        current_manager = create_maya_manager(
            project_name=project_info["name"],
            save_dir=project_info["dir"]
        )
        
        # 启动
        current_manager.start()
        print(f"切换到项目: {project_info['name']}")
    
    # 切换到项目A
    switch_to_project(projects[0])


# 示例5: 自定义配置
def example_custom_config():
    """自定义配置示例"""
    from maya_file_manager import FileManagerConfig, FileManagerApp
    
    # 创建自定义配置
    config = FileManagerConfig()
    
    # 修改配置
    config.set('base_name', 'custom_project')
    config.set('save_dir', 'D:/custom/path/')
    config.set('file_suffix', '_custom')
    config.set('version_digits', 4)  # 使用4位版本号 v0001
    config.set('window_title', '自定义文件管理器')
    config.set('future_versions_count', 10)  # 预置10个未来版本
    
    # 创建应用
    app = FileManagerApp(config)
    app.start()


# 示例6: 为不同软件适配
def example_software_adaptation():
    """软件适配示例"""
    from maya_file_manager.config import create_maya_config, create_max_config, create_blender_config
    from maya_file_manager import FileManagerApp
    
    # Maya配置
    maya_config = create_maya_config("maya_project", "D:/maya_scenes/")
    maya_app = FileManagerApp(maya_config)
    
    # 3ds Max配置
    max_config = create_max_config("max_project", "D:/max_scenes/")
    max_app = FileManagerApp(max_config)
    
    # Blender配置
    blender_config = create_blender_config("blender_project", "D:/blender_scenes/")
    blender_app = FileManagerApp(blender_config)
    
    # 根据当前环境启动相应的应用
    try:
        import maya.cmds
        maya_app.start()
        print("在Maya环境中启动")
    except ImportError:
        print("非Maya环境")


# 示例7: 配置文件管理
def example_config_management():
    """配置文件管理示例"""
    from maya_file_manager import FileManagerConfig
    
    # 创建配置
    config = FileManagerConfig()
    
    # 修改配置
    config.set('base_name', 'managed_project')
    config.set('save_dir', 'D:/managed/scenes/')
    
    # 保存配置到文件
    config.save_to_file('my_project_config.json')
    
    # 从文件加载配置
    loaded_config = FileManagerConfig('my_project_config.json')
    
    print(f"加载的项目名称: {loaded_config.get('base_name')}")


# 示例8: 观察者模式使用
def example_observer_pattern():
    """观察者模式使用示例"""
    from maya_file_manager import create_maya_manager
    
    class CustomObserver:
        def on_model_update(self, event_type, data):
            print(f"模型更新: {event_type} - {data}")
        
        def on_log_update(self, log_entry, level):
            print(f"日志更新: [{level}] {log_entry}")
    
    # 创建管理器
    manager = create_maya_manager("observed_project", "D:/observed/")
    
    # 添加自定义观察者
    observer = CustomObserver()
    controller = manager.get_controller()
    if controller:
        controller.get_model().add_observer(observer)
        controller.logger.add_observer(observer)
    
    # 启动
    manager.start()


# 示例9: 批量操作
def example_batch_operations():
    """批量操作示例"""
    from maya_file_manager import create_maya_manager
    
    # 创建管理器
    manager = create_maya_manager("batch_project", "D:/batch/")
    manager.start()
    
    # 获取模型进行批量操作
    controller = manager.get_controller()
    model = controller.get_model()
    
    # 获取所有版本信息
    versions = model.generate_version_list()
    for version in versions:
        file_info = model.get_file_info(version)
        print(f"版本 {version}: 存在={file_info['exists']}, 路径={file_info['file_path']}")


# 示例10: 错误处理
def example_error_handling():
    """错误处理示例"""
    from maya_file_manager import create_maya_manager
    
    try:
        # 尝试创建管理器
        manager = create_maya_manager("error_test", "D:/invalid/path/")
        
        if manager.start():
            print("启动成功")
        else:
            print("启动失败")
            
    except Exception as e:
        print(f"发生错误: {e}")
        # 进行错误恢复
        print("尝试使用默认配置...")
        from maya_file_manager import show_manager
        backup_manager = show_manager()


if __name__ == "__main__":
    print("Maya文件管理器使用示例")
    print("请在Maya环境中运行相应的示例函数")
    
    # 运行基本示例
    # example_basic_usage()
