@echo off
chcp 65001 >nul
echo ========================================
echo Maya文件管理器 - 自动安装脚本
echo ========================================
echo.

:: 设置变量
set "TOOL_NAME=maya_file_manager"
set "SCRIPT_NAME=maya_file_manager_mvc.py"
set "CURRENT_DIR=%~dp0"

:: 检查文件是否存在
if not exist "%CURRENT_DIR%%TOOL_NAME%" (
    echo ❌ 错误: 找不到 %TOOL_NAME% 文件夹
    echo 请确保此脚本与 %TOOL_NAME% 文件夹在同一目录下
    pause
    exit /b 1
)

if not exist "%CURRENT_DIR%%SCRIPT_NAME%" (
    echo ❌ 错误: 找不到 %SCRIPT_NAME% 文件
    echo 请确保此脚本与 %SCRIPT_NAME% 文件在同一目录下
    pause
    exit /b 1
)

echo ✅ 检测到工具文件，开始安装...
echo.

:: 查找Maya版本
echo 🔍 正在查找Maya安装路径...
set "MAYA_FOUND=0"
set "MAYA_VERSIONS=2025 2024 2023 2022"

for %%v in (%MAYA_VERSIONS%) do (
    set "MAYA_DOCS=%USERPROFILE%\Documents\maya\%%v"
    if exist "!MAYA_DOCS!" (
        echo    找到Maya %%v: !MAYA_DOCS!
        set "MAYA_SCRIPTS=!MAYA_DOCS!\scripts"
        set "MAYA_VERSION=%%v"
        set "MAYA_FOUND=1"
        goto :install
    )
)

:: 如果没找到标准路径，尝试其他可能的路径
if "%MAYA_FOUND%"=="0" (
    echo ⚠️  未找到标准Maya文档路径，尝试查找其他位置...
    
    :: 检查Maya安装目录
    for %%v in (%MAYA_VERSIONS%) do (
        if exist "C:\Program Files\Autodesk\Maya%%v" (
            set "MAYA_SCRIPTS=%USERPROFILE%\Documents\maya\%%v\scripts"
            set "MAYA_VERSION=%%v"
            echo    找到Maya %%v安装目录，使用默认scripts路径
            goto :create_and_install
        )
    )
)

if "%MAYA_FOUND%"=="0" (
    echo ❌ 未找到Maya安装，请手动安装：
    echo.
    echo 1. 将 %TOOL_NAME% 文件夹复制到Maya的scripts目录
    echo 2. 将 %SCRIPT_NAME% 复制到Maya的scripts目录
    echo 3. Maya scripts目录通常位于：
    echo    %USERPROFILE%\Documents\maya\[版本号]\scripts\
    echo.
    pause
    exit /b 1
)

:create_and_install
:: 创建scripts目录（如果不存在）
if not exist "%MAYA_SCRIPTS%" (
    echo 📁 创建scripts目录: %MAYA_SCRIPTS%
    mkdir "%MAYA_SCRIPTS%" 2>nul
    if errorlevel 1 (
        echo ❌ 无法创建scripts目录，请检查权限
        pause
        exit /b 1
    )
)

:install
echo.
echo 📦 开始安装到Maya %MAYA_VERSION%...
echo 目标路径: %MAYA_SCRIPTS%
echo.

:: 复制工具文件夹
echo 📋 复制 %TOOL_NAME% 文件夹...
if exist "%MAYA_SCRIPTS%\%TOOL_NAME%" (
    echo ⚠️  目标位置已存在旧版本，正在备份...
    if exist "%MAYA_SCRIPTS%\%TOOL_NAME%_backup" (
        rmdir /s /q "%MAYA_SCRIPTS%\%TOOL_NAME%_backup" 2>nul
    )
    move "%MAYA_SCRIPTS%\%TOOL_NAME%" "%MAYA_SCRIPTS%\%TOOL_NAME%_backup" >nul 2>&1
    echo    已备份到: %TOOL_NAME%_backup
)

xcopy "%CURRENT_DIR%%TOOL_NAME%" "%MAYA_SCRIPTS%\%TOOL_NAME%\" /E /I /Y >nul
if errorlevel 1 (
    echo ❌ 复制 %TOOL_NAME% 文件夹失败
    pause
    exit /b 1
)
echo ✅ %TOOL_NAME% 文件夹复制完成

:: 复制启动脚本
echo 📋 复制 %SCRIPT_NAME%...
if exist "%MAYA_SCRIPTS%\%SCRIPT_NAME%" (
    echo ⚠️  目标位置已存在旧版本，正在备份...
    copy "%MAYA_SCRIPTS%\%SCRIPT_NAME%" "%MAYA_SCRIPTS%\%SCRIPT_NAME%.backup" >nul 2>&1
    echo    已备份到: %SCRIPT_NAME%.backup
)

copy "%CURRENT_DIR%%SCRIPT_NAME%" "%MAYA_SCRIPTS%\" >nul
if errorlevel 1 (
    echo ❌ 复制 %SCRIPT_NAME% 失败
    pause
    exit /b 1
)
echo ✅ %SCRIPT_NAME% 复制完成

:: 创建shelf按钮脚本
echo 📋 创建shelf按钮脚本...
set "SHELF_SCRIPT=%MAYA_SCRIPTS%\maya_file_manager_shelf.py"
(
echo # -*- coding: utf-8 -*-
echo """
echo Maya文件管理器 - Shelf按钮脚本
echo 将此代码添加到Maya的shelf按钮中
echo """
echo.
echo # 启动Maya文件管理器
echo exec^(open^('maya_file_manager_mvc.py'^).read^(^)^)
) > "%SHELF_SCRIPT%"

echo ✅ Shelf按钮脚本创建完成

echo.
echo 🎉 安装完成！
echo.
echo 📋 使用方法：
echo.
echo 方法1 - 在Maya脚本编辑器中运行：
echo    exec^(open^('maya_file_manager_mvc.py'^).read^(^)^)
echo.
echo 方法2 - 在Maya Python中运行：
echo    from maya_file_manager import show_manager
echo    show_manager^(^)
echo.
echo 方法3 - 创建Shelf按钮：
echo    1. 打开Maya
echo    2. 在Shelf上右键 → "New Tab" 创建新标签页
echo    3. 右键新标签页 → "Edit Tab" → 添加按钮
echo    4. 将以下代码粘贴到按钮的Command中：
echo       exec^(open^('maya_file_manager_mvc.py'^).read^(^)^)
echo.
echo 📁 安装位置：
echo    %MAYA_SCRIPTS%
echo.
echo 💡 提示：
echo    - 重启Maya后即可使用
echo    - 如有问题，请检查Maya的脚本路径设置
echo    - 备份文件已保存，可随时恢复
echo.

pause
