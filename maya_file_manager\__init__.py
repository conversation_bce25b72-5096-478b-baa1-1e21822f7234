# -*- coding: utf-8 -*-
"""
Maya文件管理器 - MVC架构版本
作者: MMQ
版本: 2.0
支持软件: Maya 2022.5.1+

使用MVC架构重构，便于在多个软件中复用
"""

__version__ = "2.0.0"
__author__ = "MMQ"

# 导入主要模块
from .config.config import FileManagerConfig, ConfigManager, get_config
from .models.file_manager_model import FileManagerModel, MayaFileOperations, Logger
from .views.file_manager_view import FileManagerView
from .controllers.file_manager_controller import FileManagerController

# 导出主要类
__all__ = [
    'FileManagerApp',
    'FileManagerConfig',
    'FileManagerController',
    'FileManagerView',
    'FileManagerModel',
    'create_maya_manager',
    'create_custom_manager',
]


class FileManagerApp:
    """文件管理器应用程序主类"""
    
    def __init__(self, config: FileManagerConfig = None):
        """
        初始化应用程序
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or get_config()
        self.controller = None
        self._is_running = False
    
    def start(self) -> bool:
        """
        启动文件管理器
        
        Returns:
            是否启动成功
        """
        try:
            if self._is_running:
                self.stop()
            
            # 获取配置
            file_config = self.config.get_file_config()
            
            # 创建控制器
            self.controller = FileManagerController(
                save_dir=file_config['save_dir'],
                base_name=file_config['base_name'],
                config=self.config
            )
            
            # 应用UI配置
            ui_config = self.config.get_ui_config()
            view = self.controller.get_view()
            
            # 设置窗口属性
            view.setWindowTitle(ui_config['window_title'])
            view.setObjectName(ui_config['window_object_name'])
            view.setMinimumSize(*ui_config['min_window_size'])
            view.setMaximumSize(*ui_config['max_window_size'])
            view.resize(*ui_config['default_window_size'])
            
            # 显示界面
            self.controller.show(dockable=ui_config['dockable'])
            
            self._is_running = True
            return True
            
        except Exception as e:
            print(f"启动文件管理器失败: {e}")
            return False
    
    def stop(self):
        """停止文件管理器"""
        try:
            if self.controller:
                self.controller.close()
                self.controller = None
            self._is_running = False
        except Exception as e:
            print(f"停止文件管理器失败: {e}")
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._is_running
    
    def get_controller(self) -> FileManagerController:
        """获取控制器对象"""
        return self.controller
    
    def get_config(self) -> FileManagerConfig:
        """获取配置对象"""
        return self.config


# 全局应用实例
_app_instance = None


def create_maya_manager(
    project_name: str = "dev_cgtest_0010_mm",
    save_dir: str = "D:/dev/Scgtest/0010/3d/mm/",
    config_file: str = None
) -> FileManagerApp:
    """
    创建Maya文件管理器
    
    Args:
        project_name: 项目名称
        save_dir: 保存目录
        config_file: 配置文件路径
        
    Returns:
        文件管理器应用实例
    """
    # 创建或加载配置
    if config_file:
        config = FileManagerConfig(config_file)
    else:
        config = get_config()
    
    # 设置项目特定配置
    config.set('base_name', project_name)
    config.set('save_dir', save_dir)
    config = config.adapt_for_software('maya')
    
    return FileManagerApp(config)


def create_custom_manager(config: FileManagerConfig) -> FileManagerApp:
    """
    使用自定义配置创建文件管理器
    
    Args:
        config: 自定义配置对象
        
    Returns:
        文件管理器应用实例
    """
    return FileManagerApp(config)


def show_manager(
    project_name: str = "dev_cgtest_0010_mm",
    save_dir: str = "D:/dev/Scgtest/0010/3d/mm/"
) -> FileManagerApp:
    """
    显示文件管理器（便捷函数）
    
    Args:
        project_name: 项目名称
        save_dir: 保存目录
        
    Returns:
        文件管理器应用实例
    """
    global _app_instance
    
    try:
        # 关闭现有实例
        if _app_instance and _app_instance.is_running():
            _app_instance.stop()
        
        # 创建新实例
        _app_instance = create_maya_manager(project_name, save_dir)
        
        # 启动应用
        if _app_instance.start():
            print("✅ Maya文件管理器启动成功！")
            return _app_instance
        else:
            print("❌ Maya文件管理器启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return None


def close_manager():
    """关闭文件管理器"""
    global _app_instance
    
    try:
        if _app_instance:
            _app_instance.stop()
            _app_instance = None
        print("文件管理器已关闭")
    except Exception as e:
        print(f"关闭文件管理器失败: {e}")


def get_current_manager() -> FileManagerApp:
    """获取当前运行的文件管理器实例"""
    return _app_instance


# 兼容性函数（保持与原版本的兼容性）
def close_existing():
    """关闭已存在的窗口（兼容性函数）"""
    try:
        import maya.cmds as cmds
        for name in ["MayaFileManagerCompactWorkspaceControl", "MayaFileManagerCompact"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
                print(f"已删除: {name}")
    except:
        pass


# 中文函数（保持兼容性）
重启文件管理器 = lambda: show_manager()
关闭现有窗口 = close_existing
关闭文件管理器 = close_manager


# 如果直接运行此模块，则自动启动
if __name__ == "__main__":
    try:
        import maya.cmds as cmds
        manager = show_manager()
        if manager:
            print("✅ Maya文件管理器启动成功")
        else:
            print("❌ 启动失败")
    except ImportError:
        print("⚠️  请在Maya环境中运行")
    except Exception as e:
        print(f"❌ 启动异常: {e}")
