# -*- coding: utf-8 -*-
"""
Maya文件管理器 - 配置管理模块
便于在不同软件和项目中复用时修改配置
"""

import os
import json
from typing import Dict, Any, Optional


class FileManagerConfig:
    """文件管理器配置类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 文件设置
        "save_dir": "D:/dev/Scgtest/0010/3d/mm/",
        "base_name": "dev_cgtest_0010_mm",
        "file_extension": ".ma",  # Maya ASCII格式
        "file_suffix": "_mmq",
        
        # 版本设置
        "version_prefix": "v",
        "version_digits": 3,  # 版本号位数，如v001
        "future_versions_count": 5,  # 预置的未来版本数量
        
        # UI设置
        "window_title": "Maya文件管理器",
        "window_object_name": "MayaFileManagerCompact",
        "min_window_size": [450, 400],
        "max_window_size": [800, 800],
        "default_window_size": [500, 500],
        
        # 样式设置
        "theme": "maya_dark",  # 主题名称
        "log_expanded_default": False,  # 日志区域默认是否展开
        
        # 软件特定设置
        "software": "maya",  # 目标软件：maya, 3dsmax, blender等
        "dockable": True,  # 是否支持停靠
        
        # 文件操作设置
        "auto_backup": False,  # 是否自动备份
        "backup_count": 3,  # 备份文件数量
        "confirm_overwrite": True,  # 覆盖文件时是否确认
    }
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file
        self.config = self.DEFAULT_CONFIG.copy()
        
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
    
    def load_from_file(self, config_file: str) -> bool:
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                self.config.update(file_config)
            return True
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def save_to_file(self, config_file: str) -> bool:
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default=None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键名，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键名，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置最后一级的值
        config[keys[-1]] = value
    
    def get_file_pattern(self) -> str:
        """
        获取文件名模式
        
        Returns:
            文件名模式字符串
        """
        base_name = self.get('base_name')
        version_prefix = self.get('version_prefix')
        version_digits = self.get('version_digits')
        file_suffix = self.get('file_suffix')
        file_extension = self.get('file_extension')
        
        return f"{base_name}_{version_prefix}{{version:0{version_digits}d}}{file_suffix}{file_extension}"
    
    def format_filename(self, version_number: int) -> str:
        """
        格式化文件名
        
        Args:
            version_number: 版本号数字
            
        Returns:
            格式化的文件名
        """
        base_name = self.get('base_name')
        version_prefix = self.get('version_prefix')
        version_digits = self.get('version_digits')
        file_suffix = self.get('file_suffix')
        file_extension = self.get('file_extension')
        
        version_str = f"{version_prefix}{version_number:0{version_digits}d}"
        return f"{base_name}_{version_str}{file_suffix}{file_extension}"
    
    def parse_version_from_filename(self, filename: str) -> Optional[str]:
        """
        从文件名解析版本号
        
        Args:
            filename: 文件名
            
        Returns:
            版本号字符串，如果解析失败返回None
        """
        base_name = self.get('base_name')
        version_prefix = self.get('version_prefix')
        file_extension = self.get('file_extension')
        
        if not filename.startswith(base_name) or not filename.endswith(file_extension):
            return None
        
        try:
            parts = filename.split('_')
            for part in parts:
                if part.startswith(version_prefix) and part[len(version_prefix):].isdigit():
                    return part
        except:
            pass
        
        return None
    
    def get_ui_config(self) -> Dict[str, Any]:
        """
        获取UI相关配置
        
        Returns:
            UI配置字典
        """
        return {
            'window_title': self.get('window_title'),
            'window_object_name': self.get('window_object_name'),
            'min_window_size': self.get('min_window_size'),
            'max_window_size': self.get('max_window_size'),
            'default_window_size': self.get('default_window_size'),
            'theme': self.get('theme'),
            'log_expanded_default': self.get('log_expanded_default'),
            'dockable': self.get('dockable'),
        }
    
    def get_file_config(self) -> Dict[str, Any]:
        """
        获取文件相关配置
        
        Returns:
            文件配置字典
        """
        return {
            'save_dir': self.get('save_dir'),
            'base_name': self.get('base_name'),
            'file_extension': self.get('file_extension'),
            'file_suffix': self.get('file_suffix'),
            'version_prefix': self.get('version_prefix'),
            'version_digits': self.get('version_digits'),
            'future_versions_count': self.get('future_versions_count'),
            'auto_backup': self.get('auto_backup'),
            'backup_count': self.get('backup_count'),
            'confirm_overwrite': self.get('confirm_overwrite'),
        }
    
    def create_project_config(self, project_name: str, save_dir: str) -> 'FileManagerConfig':
        """
        创建项目特定的配置
        
        Args:
            project_name: 项目名称
            save_dir: 保存目录
            
        Returns:
            新的配置实例
        """
        new_config = FileManagerConfig()
        new_config.config = self.config.copy()
        new_config.set('base_name', project_name)
        new_config.set('save_dir', save_dir)
        return new_config
    
    def adapt_for_software(self, software: str) -> 'FileManagerConfig':
        """
        为特定软件适配配置
        
        Args:
            software: 软件名称 (maya, 3dsmax, blender等)
            
        Returns:
            适配后的配置实例
        """
        new_config = FileManagerConfig()
        new_config.config = self.config.copy()
        new_config.set('software', software)
        
        # 根据软件调整特定设置
        if software == 'maya':
            new_config.set('file_extension', '.ma')
            new_config.set('window_title', 'Maya文件管理器')
            new_config.set('dockable', True)
        elif software == '3dsmax':
            new_config.set('file_extension', '.max')
            new_config.set('window_title', '3ds Max文件管理器')
            new_config.set('dockable', False)
        elif software == 'blender':
            new_config.set('file_extension', '.blend')
            new_config.set('window_title', 'Blender文件管理器')
            new_config.set('dockable', False)
        
        return new_config


class ConfigManager:
    """配置管理器单例"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_config(cls, config_file: Optional[str] = None) -> FileManagerConfig:
        """
        获取配置实例
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            配置实例
        """
        if cls._config is None:
            cls._config = FileManagerConfig(config_file)
        return cls._config
    
    @classmethod
    def set_config(cls, config: FileManagerConfig):
        """
        设置配置实例
        
        Args:
            config: 配置实例
        """
        cls._config = config
    
    @classmethod
    def reset_config(cls):
        """重置配置为默认值"""
        cls._config = None


# 便捷函数
def get_config(config_file: Optional[str] = None) -> FileManagerConfig:
    """获取配置实例"""
    return ConfigManager.get_config(config_file)


def create_maya_config(project_name: str, save_dir: str) -> FileManagerConfig:
    """创建Maya项目配置"""
    base_config = get_config()
    return base_config.create_project_config(project_name, save_dir).adapt_for_software('maya')


def create_max_config(project_name: str, save_dir: str) -> FileManagerConfig:
    """创建3ds Max项目配置"""
    base_config = get_config()
    return base_config.create_project_config(project_name, save_dir).adapt_for_software('3dsmax')


def create_blender_config(project_name: str, save_dir: str) -> FileManagerConfig:
    """创建Blender项目配置"""
    base_config = get_config()
    return base_config.create_project_config(project_name, save_dir).adapt_for_software('blender')
