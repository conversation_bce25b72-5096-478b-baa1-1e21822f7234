# -*- coding: utf-8 -*-
"""
Maya文件管理器 - Controller层
负责协调Model和View，处理用户交互逻辑
"""

from typing import Optional
from ..models.file_manager_model import FileManagerModel, MayaFileOperations, Logger
from ..views.file_manager_view import FileManagerView


class FileManagerController:
    """文件管理器控制器"""

    def __init__(self, save_dir: str, base_name: str, config=None):
        """
        初始化控制器

        Args:
            save_dir: 保存目录路径
            base_name: 文件基础名称
            config: 配置对象
        """
        # 初始化模型
        self.config = config
        self.model = FileManagerModel(save_dir, base_name, config)
        self.file_ops = MayaFileOperations()
        self.logger = Logger()
        
        # 初始化视图
        self.view = FileManagerView()
        
        # 设置观察者关系
        self._setup_observers()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 初始化界面
        self._initialize_view()
    
    def _setup_observers(self):
        """设置观察者关系"""
        # Model观察者
        self.model.add_observer(self.view)
        self.model.add_observer(self)
        
        # Logger观察者
        self.logger.add_observer(self.view)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 连接视图信号到控制器方法
        self.view.version_selected.connect(self.on_version_selected)
        self.view.open_file_requested.connect(self.on_open_file_requested)
        self.view.save_file_requested.connect(self.on_save_file_requested)
        self.view.open_location_requested.connect(self.on_open_location_requested)
    
    def _initialize_view(self):
        """初始化视图"""
        self.update_file_list()
        self.logger.log("Maya文件管理器启动成功")
    
    def update_file_list(self):
        """更新文件列表"""
        try:
            # 获取版本列表
            versions = self.model.generate_version_list()
            existing_versions = self.model.get_existing_versions()
            latest_version = existing_versions[0] if existing_versions else None
            
            # 更新视图
            self.view.update_version_list(versions, existing_versions, latest_version, self.config)
            
            # 设置默认选择
            default_version = self.model.get_default_version()
            if default_version:
                self.view.set_default_version(default_version)
                # 触发选择事件以更新信息显示
                self.on_version_selected(default_version)
            
            self.logger.log(f"版本选择器已更新，共 {len(versions)} 个版本可选")
            
            if latest_version:
                self.logger.log(f"默认选择最新版本: {latest_version}")
            else:
                self.logger.log("路径下无文件，默认选择第一版: v001")
                
        except Exception as e:
            error_msg = f"更新版本选择器错误: {e}"
            self.logger.log(error_msg)
            self.view.show_message("错误", error_msg, "error")
    
    def on_version_selected(self, version: str):
        """
        处理版本选择事件
        
        Args:
            version: 选择的版本号
        """
        try:
            if not self.model.validate_version(version):
                self.logger.log(f"无效的版本号: {version}")
                return
            
            # 获取文件信息
            file_info = self.model.get_file_info(version)
            
            # 更新视图显示
            self.view.update_selection_info(file_info)
            
            self.logger.log(f"选择版本: {version}")
            
        except Exception as e:
            error_msg = f"版本选择回调错误: {e}"
            self.logger.log(error_msg)
            self.view.show_message("错误", error_msg, "error")
    
    def on_open_file_requested(self, version: str):
        """
        处理打开文件请求
        
        Args:
            version: 要打开的版本号
        """
        try:
            if not version:
                self.view.show_message("未选择文件", "请先选择要打开的文件版本", "warning")
                return
            
            file_path = self.model.get_file_path(version)
            file_info = self.model.get_file_info(version)
            
            if not file_info['exists']:
                self.view.show_message(
                    "文件不存在", 
                    f"版本 {version} 的文件不存在:\n{file_info['filename']}", 
                    "warning"
                )
                return
            
            # 检查当前场景是否有未保存的更改
            if self.file_ops.is_scene_modified():
                if not self.view.show_question(
                    "确认", 
                    "当前场景有未保存的更改，是否继续打开？"
                ):
                    self.logger.log("用户取消打开操作")
                    return
            
            # 打开文件
            success, message = self.file_ops.open_file(file_path)
            
            if success:
                self.logger.log(f"成功打开: {file_info['filename']}")
                self.view.show_message(
                    "打开成功", 
                    f"已打开版本 {version}:\n{file_info['filename']}", 
                    "info"
                )
            else:
                self.logger.log(f"打开文件失败: {message}")
                self.view.show_message("打开失败", message, "error")
                
        except Exception as e:
            error_msg = f"打开文件失败: {str(e)}"
            self.logger.log(error_msg)
            self.view.show_message("打开失败", error_msg, "error")
    
    def on_save_file_requested(self, version: str):
        """
        处理保存文件请求
        
        Args:
            version: 要保存的版本号
        """
        try:
            if not version:
                self.view.show_message("未选择版本", "请先选择要保存的版本", "warning")
                return
            
            file_path = self.model.get_file_path(version)
            file_info = self.model.get_file_info(version)
            
            # 确保目录存在
            if not self.model.ensure_directory_exists():
                return
            
            # 检查文件是否存在，询问覆盖
            if file_info['exists']:
                if not self.view.show_question(
                    "文件已存在",
                    f"版本 {version} 已存在，是否覆盖？\n\n{file_info['filename']}"
                ):
                    self.logger.log("用户取消覆盖")
                    return
            
            # 保存文件
            self.logger.log(f"正在保存到版本 {version}: {file_info['filename']}")
            
            success, message = self.file_ops.save_file(file_path)
            
            if success:
                self.logger.log(f"保存成功: {file_info['filename']}")
                self.update_file_list()  # 刷新版本选择器
                self.view.show_message(
                    "保存成功", 
                    f"文件已保存为版本 {version}:\n{file_info['filename']}", 
                    "info"
                )
            else:
                self.logger.log(f"保存文件失败: {message}")
                self.view.show_message("保存失败", message, "error")
                
        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.logger.log(error_msg)
            self.view.show_message("保存失败", error_msg, "error")
    
    def on_open_location_requested(self, file_path: str):
        """
        处理打开文件位置请求
        
        Args:
            file_path: 文件路径
        """
        try:
            if file_path:
                self.view.open_file_location(file_path, self.model.save_dir)
                directory = self.model.save_dir
                self.logger.log(f"已打开文件位置: {directory}")
            else:
                # 如果没有选择文件，打开默认保存目录
                self.view.open_file_location("", self.model.save_dir)
                self.logger.log(f"已打开默认保存目录: {self.model.save_dir}")
                
        except Exception as e:
            error_msg = f"打开文件位置失败: {str(e)}"
            self.logger.log(error_msg)
            self.view.show_message("错误", error_msg, "error")
    
    def show(self, dockable: bool = True):
        """
        显示文件管理器
        
        Args:
            dockable: 是否可停靠
        """
        self.view.show(dockable=dockable)
    
    def close(self):
        """关闭文件管理器"""
        try:
            if self.view:
                self.view.close()
                self.view.deleteLater()
        except:
            pass
    
    def on_model_update(self, event_type: str, data=None):
        """
        响应模型更新事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type == 'directory_created':
            self.logger.log(f"已创建目录: {data}")
        elif event_type == 'error':
            self.logger.log(f"错误: {data}")
            self.view.show_message("错误", str(data), "error")
    
    def get_view(self) -> FileManagerView:
        """获取视图对象"""
        return self.view
    
    def get_model(self) -> FileManagerModel:
        """获取模型对象"""
        return self.model
