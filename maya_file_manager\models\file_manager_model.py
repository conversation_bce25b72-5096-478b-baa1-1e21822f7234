# -*- coding: utf-8 -*-
"""
Maya文件管理器 - Model层
负责文件版本管理、文件操作等业务逻辑
"""

import os
import datetime
from typing import List, Optional, Tuple


class FileManagerModel:
    """文件管理器数据模型"""

    def __init__(self, save_dir: str, base_name: str, config=None):
        """
        初始化文件管理器模型

        Args:
            save_dir: 保存目录路径
            base_name: 文件基础名称
            config: 配置对象
        """
        self.save_dir = save_dir
        self.base_name = base_name
        self.config = config
        self._observers = []  # 观察者列表，用于通知View更新
    
    def add_observer(self, observer):
        """添加观察者"""
        self._observers.append(observer)
    
    def remove_observer(self, observer):
        """移除观察者"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notify_observers(self, event_type: str, data=None):
        """通知所有观察者"""
        for observer in self._observers:
            if hasattr(observer, 'on_model_update'):
                observer.on_model_update(event_type, data)
    
    def get_existing_versions(self) -> List[str]:
        """
        获取已存在的版本号
        
        Returns:
            版本号列表，按降序排列
        """
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for f in os.listdir(self.save_dir):
                    if f.startswith(self.base_name) and f.endswith(('.ma', '.mb')):
                        parts = f.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except Exception as e:
                self.notify_observers('error', f"获取版本列表失败: {e}")
        
        return sorted(list(set(versions)), reverse=True)
    
    def generate_version_list(self) -> List[str]:
        """
        生成版本列表 - 包含现有版本和预置的未来版本
        
        Returns:
            完整的版本列表
        """
        existing_versions = self.get_existing_versions()
        
        # 找到最大版本号
        max_version_num = 0
        for ver in existing_versions:
            try:
                num = int(ver[1:])  # 去掉'v'前缀
                max_version_num = max(max_version_num, num)
            except:
                pass
        
        # 如果没有现有版本，确保从v001开始
        if max_version_num == 0:
            max_version_num = 0
        
        # 生成版本列表：现有版本 + 预置5个未来版本
        all_versions = []
        
        # 添加现有版本
        all_versions.extend(existing_versions)
        
        # 添加未来版本（从最大版本号+1开始，预置5个）
        start_version = max(max_version_num + 1, 1)
        for i in range(start_version, start_version + 5):
            version = f"v{i:03d}"
            if version not in existing_versions:
                all_versions.append(version)
        
        # 按版本号降序排列（最新的在前）
        all_versions.sort(key=lambda x: int(x[1:]), reverse=True)
        return all_versions
    
    def get_file_path(self, version: str) -> str:
        """
        获取指定版本的文件路径

        Args:
            version: 版本号

        Returns:
            文件完整路径
        """
        if self.config:
            # 从版本字符串中提取数字
            try:
                version_num = int(version[1:]) if version.startswith('v') else int(version)
                filename = self.config.format_filename(version_num)
            except:
                # 如果解析失败，使用默认格式
                filename = f"{self.base_name}_{version}_mmq.ma"
        else:
            filename = f"{self.base_name}_{version}_mmq.ma"
        return os.path.join(self.save_dir, filename)
    
    def get_file_info(self, version: str) -> dict:
        """
        获取文件信息
        
        Args:
            version: 版本号
            
        Returns:
            文件信息字典
        """
        file_path = self.get_file_path(version)
        filename = os.path.basename(file_path)
        exists = os.path.exists(file_path)
        
        info = {
            'version': version,
            'filename': filename,
            'file_path': file_path,
            'exists': exists,
            'is_latest': False,
            'size_mb': 0,
            'modified_time': None
        }
        
        if exists:
            try:
                info['size_mb'] = os.path.getsize(file_path) / 1024 / 1024
                info['modified_time'] = datetime.datetime.fromtimestamp(
                    os.path.getmtime(file_path)
                ).strftime("%Y-%m-%d %H:%M")
            except:
                pass
        
        # 判断是否为最新版本
        existing_versions = self.get_existing_versions()
        if existing_versions and version == existing_versions[0]:
            info['is_latest'] = True
        
        return info
    
    def ensure_directory_exists(self) -> bool:
        """
        确保保存目录存在
        
        Returns:
            是否成功创建或目录已存在
        """
        if not os.path.exists(self.save_dir):
            try:
                os.makedirs(self.save_dir)
                self.notify_observers('directory_created', self.save_dir)
                return True
            except Exception as e:
                self.notify_observers('error', f"无法创建目录: {e}")
                return False
        return True
    
    def get_default_version(self) -> Optional[str]:
        """
        获取默认选择的版本
        
        Returns:
            默认版本号，如果有已存在文件则返回最新版本，否则返回v001
        """
        existing_versions = self.get_existing_versions()
        if existing_versions:
            return existing_versions[0]  # 最新版本
        else:
            return "v001"  # 第一版
    
    def validate_version(self, version: str) -> bool:
        """
        验证版本号格式
        
        Args:
            version: 版本号
            
        Returns:
            是否为有效的版本号格式
        """
        if not version or not version.startswith('v'):
            return False
        
        try:
            int(version[1:])
            return True
        except:
            return False


class MayaFileOperations:
    """Maya文件操作类"""
    
    @staticmethod
    def open_file(file_path: str) -> Tuple[bool, str]:
        """
        打开Maya文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            import maya.cmds as cmds
            
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            # 检查当前场景是否有未保存的更改
            if cmds.file(query=True, modified=True):
                # 这里应该由Controller处理用户确认
                pass
            
            cmds.file(file_path, open=True, force=True)
            return True, "文件打开成功"
            
        except Exception as e:
            return False, f"打开文件失败: {str(e)}"
    
    @staticmethod
    def save_file(file_path: str) -> Tuple[bool, str]:
        """
        保存Maya文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            import maya.cmds as cmds
            
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")
            return True, "文件保存成功"
            
        except Exception as e:
            return False, f"保存文件失败: {str(e)}"
    
    @staticmethod
    def is_scene_modified() -> bool:
        """
        检查当前场景是否有未保存的更改
        
        Returns:
            是否有未保存的更改
        """
        try:
            import maya.cmds as cmds
            return cmds.file(query=True, modified=True)
        except:
            return False


class Logger:
    """日志记录类"""
    
    def __init__(self):
        self._observers = []
    
    def add_observer(self, observer):
        """添加日志观察者"""
        self._observers.append(observer)
    
    def remove_observer(self, observer):
        """移除日志观察者"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def log(self, message: str, level: str = "INFO"):
        """
        记录日志
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 输出到控制台
        print(f"[文件管理器] {message}")
        
        # 通知观察者
        for observer in self._observers:
            if hasattr(observer, 'on_log_update'):
                observer.on_log_update(log_entry, level)
