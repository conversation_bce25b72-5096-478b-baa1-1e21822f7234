# -*- coding: utf-8 -*-
"""
Maya文件管理器 - View层
负责UI界面展示和用户交互界面
"""

import os
import platform
import subprocess
from typing import Optional, Callable

try:
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    MAYA_AVAILABLE = True
except ImportError as e:
    print(f"导入Qt模块失败: {e}")
    MAYA_AVAILABLE = False
    # 创建虚拟基类以避免导入错误
    class MayaQWidgetDockableMixin:
        def show(self, dockable=True):
            print("虚拟显示方法 - 需要Maya环境")
        def close(self):
            pass
        def deleteLater(self):
            pass

    class QWidget:
        def __init__(self):
            pass
        def setWindowTitle(self, title):
            pass
        def setMinimumSize(self, w, h):
            pass
        def setMaximumSize(self, w, h):
            pass
        def resize(self, w, h):
            pass
        def setObjectName(self, name):
            pass
        def setStyleSheet(self, style):
            pass
        def setCursor(self, cursor):
            pass

    class Signal:
        def __init__(self, *args):
            self._callbacks = []
        def connect(self, func):
            self._callbacks.append(func)
        def emit(self, *args):
            for callback in self._callbacks:
                try:
                    callback(*args)
                except:
                    pass

    # 添加更多虚拟Qt类
    class QVBoxLayout:
        def __init__(self, parent=None):
            pass
        def setSpacing(self, spacing):
            pass
        def setContentsMargins(self, *args):
            pass
        def addWidget(self, widget):
            pass
        def addLayout(self, layout):
            pass
        def addSpacing(self, spacing):
            pass
        def addStretch(self):
            pass

    class QHBoxLayout:
        def __init__(self):
            pass
        def setSpacing(self, spacing):
            pass
        def addWidget(self, widget):
            pass
        def addStretch(self):
            pass

    class QLabel:
        def __init__(self, text=""):
            pass
        def setStyleSheet(self, style):
            pass

    class QComboBox:
        def __init__(self):
            self.currentTextChanged = Signal(str)
        def setFixedHeight(self, height):
            pass
        def setSizePolicy(self, *args):
            pass
        def setStyleSheet(self, style):
            pass
        def clear(self):
            pass
        def addItem(self, text, data=None):
            pass
        def count(self):
            return 0
        def currentIndex(self):
            return 0
        def setCurrentIndex(self, index):
            pass
        def itemData(self, index):
            return None
        def model(self):
            return None

    class QTextEdit:
        def __init__(self):
            self.mousePressEvent = None
        def setMinimumHeight(self, height):
            pass
        def setMaximumHeight(self, height):
            pass
        def setSizePolicy(self, *args):
            pass
        def setStyleSheet(self, style):
            pass
        def setReadOnly(self, readonly):
            pass
        def setPlainText(self, text):
            pass
        def append(self, text):
            pass
        def hide(self):
            pass
        def show(self):
            pass
        def setCursor(self, cursor):
            pass
        def verticalScrollBar(self):
            return type('ScrollBar', (), {'setValue': lambda self, x: None, 'maximum': lambda self: 0})()

    class QPushButton:
        def __init__(self, text=""):
            self.clicked = Signal()
        def setFixedHeight(self, height):
            pass
        def setSizePolicy(self, *args):
            pass
        def setStyleSheet(self, style):
            pass
        def setText(self, text):
            pass

    class QMessageBox:
        @staticmethod
        def information(parent, title, message):
            print(f"Info: {title} - {message}")
        @staticmethod
        def warning(parent, title, message):
            print(f"Warning: {title} - {message}")
        @staticmethod
        def critical(parent, title, message):
            print(f"Error: {title} - {message}")
        @staticmethod
        def question(parent, title, message, buttons=None, default=None):
            print(f"Question: {title} - {message}")
            return True
        Yes = True
        No = False

    class QSizePolicy:
        Expanding = "Expanding"
        Fixed = "Fixed"

    class Qt:
        PointingHandCursor = "PointingHandCursor"

    class QColor:
        def __init__(self, r, g, b):
            pass


class FileManagerView(MayaQWidgetDockableMixin if MAYA_AVAILABLE else QWidget):
    """文件管理器视图类"""

    # 定义信号
    version_selected = Signal(str)  # 版本选择信号
    open_file_requested = Signal(str)  # 打开文件请求信号
    save_file_requested = Signal(str)  # 保存文件请求信号
    open_location_requested = Signal(str)  # 打开文件位置请求信号
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Maya文件管理器")
        self.setMinimumSize(450, 400)
        self.setMaximumSize(800, 800)
        self.resize(500, 500)
        self.setObjectName("MayaFileManagerCompact")
        
        self.log_expanded = False
        self.current_file_path = ""
        
        self._setup_ui()
        self._setup_styles()
    
    def _setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title = QLabel("Maya文件管理器")
        title.setStyleSheet(
            "font-size:14px;font-weight:bold;color:#FFFFFF;"
            "padding:8px 0;border-bottom:1px solid #5A5A5A;margin-bottom:8px;"
        )
        layout.addWidget(title)
        
        # 文件版本选择器标签
        list_label = QLabel("文件版本选择器:")
        list_label.setStyleSheet("font-weight:bold;margin-bottom:4px;")
        layout.addWidget(list_label)
        
        # 版本选择器
        self.version_selector = self._create_version_selector()
        layout.addWidget(self.version_selector)
        
        # 当前选择信息
        self.selection_info = self._create_selection_info()
        layout.addWidget(self.selection_info)
        
        # 按钮区域
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
        
        layout.addSpacing(10)
        
        # 日志区域
        log_layout = self._create_log_layout()
        layout.addLayout(log_layout)
        
        # 日志文本区域
        self.log_text = self._create_log_text()
        layout.addWidget(self.log_text)
        
        layout.addStretch()
    
    def _create_version_selector(self) -> QComboBox:
        """创建版本选择器"""
        selector = QComboBox()
        selector.setFixedHeight(40)
        selector.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        selector.currentTextChanged.connect(self._on_version_changed)
        return selector
    
    def _create_selection_info(self) -> QTextEdit:
        """创建选择信息显示区域"""
        info = QTextEdit()
        info.setMinimumHeight(80)
        info.setMaximumHeight(200)
        info.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        info.setReadOnly(True)
        info.mousePressEvent = self._on_info_clicked
        info.setCursor(Qt.PointingHandCursor)
        return info
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        self.open_btn = QPushButton("Open")
        self.open_btn.setFixedHeight(40)
        self.open_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.open_btn.clicked.connect(self._on_open_clicked)
        layout.addWidget(self.open_btn)
        
        self.save_btn = QPushButton("Save")
        self.save_btn.setFixedHeight(40)
        self.save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.save_btn.clicked.connect(self._on_save_clicked)
        layout.addWidget(self.save_btn)
        
        return layout
    
    def _create_log_layout(self) -> QHBoxLayout:
        """创建日志标题布局"""
        layout = QHBoxLayout()
        
        self.log_toggle_btn = QPushButton("▶ 操作日志")
        self.log_toggle_btn.setFixedHeight(25)
        self.log_toggle_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.log_toggle_btn.setStyleSheet(
            "text-align:left;padding:4px;font-weight:bold;"
            "border:none;background-color:transparent;"
        )
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        layout.addWidget(self.log_toggle_btn)
        layout.addStretch()
        
        return layout
    
    def _create_log_text(self) -> QTextEdit:
        """创建日志文本区域"""
        log_text = QTextEdit()
        log_text.setMinimumHeight(60)
        log_text.setMaximumHeight(150)
        log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        log_text.setReadOnly(True)
        log_text.hide()  # 默认隐藏
        return log_text
    
    def _setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget{background-color:#393939;color:#CCCCCC;font-family:"Segoe UI";font-size:11px;}
            QPushButton{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:8px 16px;border-radius:3px;font-weight:bold;}
            QPushButton:hover{background-color:#5A5A5A;}QPushButton:pressed{background-color:#3A3A3A;}
            QListWidget{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;outline:none;}
            QListWidget::item{padding:8px;border-bottom:1px solid #4A4A4A;}
            QListWidget::item:selected{background-color:#5A5A5A;color:#FFFFFF;}
            QListWidget::item:hover{background-color:#4A4A4A;}
            QScrollBar:vertical{background-color:#2A2A2A;width:12px;border:none;}
            QScrollBar::handle:vertical{background-color:#5A5A5A;border-radius:6px;min-height:20px;}
            QScrollBar::handle:vertical:hover{background-color:#6A6A6A;}
            QScrollBar::add-line:vertical,QScrollBar::sub-line:vertical{height:0px;}
            QTextEdit{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;}
            QLabel{color:#CCCCCC;background-color:transparent;}
        """)
        
        # 版本选择器样式
        self.version_selector.setStyleSheet("""
            QComboBox {
                background-color: #4A4A4A; border: 2px solid #5A5A5A;
                color: #FFFFFF; padding: 8px 12px; border-radius: 6px;
                font-size: 14px; font-weight: bold;
            }
            QComboBox:hover { border-color: #6A6A6A; }
            QComboBox::drop-down { border: none; width: 20px; }
            QComboBox::down-arrow {
                image: none; width: 0; height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #FFFFFF;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #4A4A4A; border: 2px solid #5A5A5A;
                selection-background-color: #6A6A6A; outline: none;
                font-size: 13px; padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                border-bottom: 1px solid #5A5A5A;
            }
        """)
        
        # 选择信息样式
        self.selection_info.setStyleSheet("""
            QTextEdit {
                background-color:#4A4A4A;border:1px solid #5A5A5A;border-radius:3px;
                padding:8px;font-size:11px;color:#CCCCCC;
            }
            QTextEdit:hover {
                border-color:#6A6A6A;
            }
        """)
        
        # 日志文本样式
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;
                padding:4px;border-radius:3px;font-family:monospace;font-size:10px;
            }
            QScrollBar:vertical {
                background-color:#2A2A2A;width:12px;border:none;
            }
            QScrollBar::handle:vertical {
                background-color:#5A5A5A;border-radius:6px;min-height:20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color:#6A6A6A;
            }
            QScrollBar::add-line:vertical,QScrollBar::sub-line:vertical {
                height:0px;
            }
        """)
    
    def toggle_log(self):
        """切换日志显示"""
        self.log_expanded = not self.log_expanded
        if self.log_expanded:
            self.log_text.show()
            self.log_toggle_btn.setText("▼ 操作日志")
        else:
            self.log_text.hide()
            self.log_toggle_btn.setText("▶ 操作日志")
    
    def update_version_list(self, versions: list, existing_versions: list, latest_version: str = None, config=None):
        """
        更新版本选择器

        Args:
            versions: 所有版本列表
            existing_versions: 已存在的版本列表
            latest_version: 最新版本号
            config: 配置对象
        """
        self.version_selector.clear()

        for i, ver in enumerate(versions):
            if config:
                try:
                    version_num = int(ver[1:]) if ver.startswith('v') else int(ver)
                    filename = config.format_filename(version_num)
                except:
                    filename = f"dev_cgtest_0010_mm_{ver}_mmq.ma"
            else:
                filename = f"dev_cgtest_0010_mm_{ver}_mmq.ma"
            display_text = f"{ver} - {filename}"

            self.version_selector.addItem(display_text, ver)
            
            # 设置颜色
            try:
                item = self.version_selector.model().item(i)
                exists = ver in existing_versions
                
                if exists:
                    if ver == latest_version:
                        # 最新版本 - 绿色
                        item.setForeground(QColor(46, 204, 113))
                    else:
                        # 历史版本 - 红色
                        item.setForeground(QColor(231, 76, 60))
                else:
                    # 未创建版本 - 白色
                    item.setForeground(QColor(255, 255, 255))
            except:
                pass
    
    def set_default_version(self, version: str):
        """设置默认选择的版本"""
        for i in range(self.version_selector.count()):
            if self.version_selector.itemData(i) == version:
                self.version_selector.setCurrentIndex(i)
                break
    
    def update_selection_info(self, file_info: dict):
        """更新选择信息显示"""
        version = file_info['version']
        filename = file_info['filename']
        file_path = file_info['file_path']
        exists = file_info['exists']
        is_latest = file_info['is_latest']
        
        # 设置状态颜色
        if exists:
            status_color = "🟢" if is_latest else "🔴"
            color = "color: #2ECC71;" if is_latest else "color: #E74C3C;"
        else:
            status_color = "⚪"
            color = "color: #FFFFFF;"
        
        # 更新选择器颜色
        self._update_selector_color(color)
        
        # 构建状态文本
        status_text = f"当前选择: {status_color} {version}\n文件名: {filename}"
        
        if exists:
            if file_info['size_mb'] and file_info['modified_time']:
                status_text += f"\n状态: 文件已存在\n大小: {file_info['size_mb']:.2f} MB\n修改时间: {file_info['modified_time']}"
            else:
                status_text += "\n状态: 文件已存在"
        else:
            status_text += "\n状态: 新版本 (文件不存在)"
        
        status_text += f"\n\n📁 文件路径:\n{file_path}\n(点击此区域打开文件夹)"
        
        self.selection_info.setPlainText(status_text)
        self.current_file_path = file_path
    
    def _update_selector_color(self, color: str):
        """更新选择器颜色"""
        try:
            current_style = self.version_selector.styleSheet()
            import re
            new_style = re.sub(r'color:\s*[^;]+;', '', current_style)
            new_style = new_style.replace('QComboBox {', f'QComboBox {{ {color}')
            self.version_selector.setStyleSheet(new_style)
        except:
            pass
    
    def append_log(self, message: str):
        """添加日志消息"""
        self.log_text.append(message)
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息对话框"""
        if msg_type == "info":
            QMessageBox.information(self, title, message)
        elif msg_type == "warning":
            QMessageBox.warning(self, title, message)
        elif msg_type == "error":
            QMessageBox.critical(self, title, message)
    
    def show_question(self, title: str, message: str) -> bool:
        """显示确认对话框"""
        reply = QMessageBox.question(
            self, title, message,
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        return reply == QMessageBox.Yes
    
    def open_file_location(self, file_path: str, save_dir: str):
        """打开文件位置"""
        try:
            directory = os.path.dirname(file_path)
            
            # 确保目录存在
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                except Exception as e:
                    self.show_message("错误", f"无法创建目录: {str(e)}", "error")
                    return
            
            # 根据操作系统打开文件夹
            system = platform.system()
            if system == "Windows":
                if os.path.exists(file_path):
                    subprocess.run(['explorer', '/select,', os.path.normpath(file_path)])
                else:
                    subprocess.run(['explorer', os.path.normpath(directory)])
            elif system == "Darwin":  # macOS
                if os.path.exists(file_path):
                    subprocess.run(['open', '-R', file_path])
                else:
                    subprocess.run(['open', directory])
            else:  # Linux
                subprocess.run(['xdg-open', directory])
                
        except Exception as e:
            self.show_message("错误", f"打开文件位置失败: {str(e)}", "error")
    
    # 事件处理方法
    def _on_version_changed(self, display_text: str):
        """版本选择改变事件"""
        if display_text:
            current_index = self.version_selector.currentIndex()
            version = self.version_selector.itemData(current_index)
            if version:
                self.version_selected.emit(version)
    
    def _on_open_clicked(self):
        """打开按钮点击事件"""
        current_index = self.version_selector.currentIndex()
        if current_index >= 0:
            version = self.version_selector.itemData(current_index)
            if version:
                self.open_file_requested.emit(version)
    
    def _on_save_clicked(self):
        """保存按钮点击事件"""
        current_index = self.version_selector.currentIndex()
        if current_index >= 0:
            version = self.version_selector.itemData(current_index)
            if version:
                self.save_file_requested.emit(version)
    
    def _on_info_clicked(self, event):
        """信息区域点击事件"""
        if self.current_file_path:
            self.open_location_requested.emit(self.current_file_path)
    
    # 观察者模式方法
    def on_model_update(self, event_type: str, data=None):
        """响应模型更新"""
        # 这里可以根据不同的事件类型进行相应的UI更新
        pass
    
    def on_log_update(self, log_entry: str, level: str = "INFO"):
        """响应日志更新"""
        self.append_log(log_entry)
