# -*- coding: utf-8 -*-
"""
Maya文件管理器 - MVC架构版本 | 作者: MMQ | Maya 2022.5.1
这是使用MVC架构重构的版本，便于在多个软件中复用

使用方法:
1. 直接运行此文件 - 使用默认配置启动
2. 导入模块使用 - 可自定义配置

示例:
    # 基本使用
    import maya_file_manager_mvc
    
    # 自定义配置使用
    from maya_file_manager import create_maya_manager
    manager = create_maya_manager("my_project", "D:/my_path/")
    manager.start()
"""

print("🚀 启动Maya文件管理器 MVC版...")

try:
    # 检查Maya环境
    import maya.cmds as cmds
    print("✅ Maya环境检测成功")
    
    # 导入文件管理器模块
    from maya_file_manager import show_manager, close_manager, close_existing
    from maya_file_manager import 重启文件管理器, 关闭文件管理器, 关闭现有窗口
    
    print("✅ 模块导入成功")
    
    # 关闭现有窗口
    close_existing()
    
    # 启动文件管理器
    print("正在启动文件管理器...")
    manager = show_manager()
    
    if manager:
        print("🎉 文件管理器界面已显示")
        print("📋 新版本特性:")
        print("   • MVC架构设计，代码结构清晰")
        print("   • 模块化组织，便于维护和扩展")
        print("   • 配置文件支持，便于多项目使用")
        print("   • 支持多软件复用 (Maya/3ds Max/Blender)")
        print("   • 观察者模式，松耦合设计")
        print("")
        print("📋 基本功能:")
        print("   • 版本选择器 - 选择文件版本")
        print("   • Open按钮 - 打开选中版本")
        print("   • Save按钮 - 保存到选中版本")
        print("   • 点击信息区域 - 打开文件夹")
        print("   • 可折叠日志 - 查看操作记录")
        print("")
        print("=" * 60)
        print("🔧 控制命令:")
        print("   重启: show_manager() 或 重启文件管理器()")
        print("   关闭: close_manager() 或 关闭文件管理器()")
        print("   清理: close_existing() 或 关闭现有窗口()")
        print("=" * 60)
        print("")
        print("📁 当前配置:")
        config = manager.get_config()
        print(f"   项目名称: {config.get('base_name')}")
        print(f"   保存目录: {config.get('save_dir')}")
        print(f"   文件格式: {config.get('file_extension')}")
        print("=" * 60)
        
    else:
        print("❌ 启动失败")

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在Maya环境中运行此脚本")
except Exception as e:
    print(f"❌ 启动异常: {e}")

print("\n💡 提示: 如需自定义配置，请参考maya_file_manager包的使用文档")
