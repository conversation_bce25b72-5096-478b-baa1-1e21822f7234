# -*- coding: utf-8 -*-
"""
Maya文件管理器 - MVC架构版本 | 作者: MMQ | Maya 2022.5.1
"""

try:
    # 检查Maya环境
    import maya.cmds as cmds

    # 导入文件管理器模块
    from maya_file_manager import show_manager, close_manager, close_existing
    from maya_file_manager import 重启文件管理器, 关闭文件管理器, 关闭现有窗口

    # 关闭现有窗口
    close_existing()

    # 启动文件管理器
    manager = show_manager()

    if manager:
        print("🎉 Maya文件管理器启动成功！")
        print("📋 功能说明:")
        print("   • 版本选择器 - 选择文件版本")
        print("   • Open按钮 - 打开选中版本")
        print("   • Save按钮 - 保存到选中版本")
        print("   • 点击信息区域 - 打开文件夹")
        print("")
        print("🔧 控制命令:")
        print("   重启: show_manager() 或 重启文件管理器()")
        print("   关闭: close_manager() 或 关闭文件管理器()")
        print("=" * 50)

        # 显示当前配置
        config = manager.get_config()
        print(f"📁 当前配置: {config.get('base_name')} → {config.get('save_dir')}")

    else:
        print("❌ 启动失败")

except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在Maya环境中运行此脚本")
except Exception as e:
    print(f"❌ 启动异常: {e}")
