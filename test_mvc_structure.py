# -*- coding: utf-8 -*-
"""
测试MVC架构的文件管理器
验证各个模块是否正常工作
"""

def test_config_module():
    """测试配置模块"""
    print("=" * 50)
    print("测试配置模块...")
    
    try:
        from maya_file_manager.config.config import FileManagerConfig, get_config
        
        # 测试默认配置
        config = get_config()
        print(f"✅ 默认配置加载成功")
        print(f"   保存目录: {config.get('save_dir')}")
        print(f"   项目名称: {config.get('base_name')}")
        print(f"   文件扩展名: {config.get('file_extension')}")
        
        # 测试配置修改
        config.set('base_name', 'test_project')
        print(f"✅ 配置修改成功: {config.get('base_name')}")
        
        # 测试文件名格式化
        filename = config.format_filename(1)
        print(f"✅ 文件名格式化: {filename}")
        
        # 测试软件适配
        maya_config = config.adapt_for_software('maya')
        print(f"✅ Maya适配: {maya_config.get('software')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        return False


def test_model_module():
    """测试模型模块"""
    print("=" * 50)
    print("测试模型模块...")
    
    try:
        from maya_file_manager.models.file_manager_model import FileManagerModel, Logger
        from maya_file_manager.config.config import get_config
        
        # 创建配置
        config = get_config()
        config.set('save_dir', 'D:/test_mvc/')
        config.set('base_name', 'test_mvc_project')
        
        # 创建模型
        model = FileManagerModel(
            save_dir=config.get('save_dir'),
            base_name=config.get('base_name'),
            config=config
        )
        print("✅ 模型创建成功")
        
        # 测试版本生成
        versions = model.generate_version_list()
        print(f"✅ 版本列表生成: {versions[:3]}...")  # 显示前3个
        
        # 测试文件路径生成
        file_path = model.get_file_path('v001')
        print(f"✅ 文件路径生成: {file_path}")
        
        # 测试文件信息获取
        file_info = model.get_file_info('v001')
        print(f"✅ 文件信息获取: 存在={file_info['exists']}")
        
        # 测试日志系统
        logger = Logger()
        logger.log("测试日志消息")
        print("✅ 日志系统正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型模块测试失败: {e}")
        return False


def test_view_module():
    """测试视图模块"""
    print("=" * 50)
    print("测试视图模块...")
    
    try:
        from maya_file_manager.views.file_manager_view import FileManagerView, MAYA_AVAILABLE
        
        if not MAYA_AVAILABLE:
            print("⚠️  非Maya环境，使用虚拟UI组件")
        
        # 创建视图
        view = FileManagerView()
        print("✅ 视图创建成功")
        
        # 测试版本列表更新
        versions = ['v003', 'v002', 'v001']
        existing_versions = ['v002', 'v001']
        view.update_version_list(versions, existing_versions, 'v002')
        print("✅ 版本列表更新成功")
        
        # 测试文件信息更新
        file_info = {
            'version': 'v001',
            'filename': 'test_mvc_project_v001_mmq.ma',
            'file_path': 'D:/test_mvc/test_mvc_project_v001_mmq.ma',
            'exists': True,
            'is_latest': False,
            'size_mb': 1.5,
            'modified_time': '2024-01-01 12:00'
        }
        view.update_selection_info(file_info)
        print("✅ 文件信息更新成功")
        
        # 测试日志添加
        view.append_log("测试日志消息")
        print("✅ 日志添加成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 视图模块测试失败: {e}")
        return False


def test_controller_module():
    """测试控制器模块"""
    print("=" * 50)
    print("测试控制器模块...")
    
    try:
        from maya_file_manager.controllers.file_manager_controller import FileManagerController
        from maya_file_manager.config.config import get_config
        
        # 创建配置
        config = get_config()
        config.set('save_dir', 'D:/test_mvc/')
        config.set('base_name', 'test_mvc_project')
        
        # 创建控制器
        controller = FileManagerController(
            save_dir=config.get('save_dir'),
            base_name=config.get('base_name'),
            config=config
        )
        print("✅ 控制器创建成功")
        
        # 测试文件列表更新
        controller.update_file_list()
        print("✅ 文件列表更新成功")
        
        # 测试版本选择
        controller.on_version_selected('v001')
        print("✅ 版本选择处理成功")
        
        # 获取模型和视图
        model = controller.get_model()
        view = controller.get_view()
        print(f"✅ 模型和视图获取成功: Model={type(model).__name__}, View={type(view).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 控制器模块测试失败: {e}")
        return False


def test_main_app():
    """测试主应用"""
    print("=" * 50)
    print("测试主应用...")
    
    try:
        from maya_file_manager import FileManagerApp, FileManagerConfig
        
        # 创建配置
        config = FileManagerConfig()
        config.set('save_dir', 'D:/test_mvc/')
        config.set('base_name', 'test_mvc_app')
        
        # 创建应用
        app = FileManagerApp(config)
        print("✅ 应用创建成功")
        
        # 测试配置获取
        app_config = app.get_config()
        print(f"✅ 配置获取成功: {app_config.get('base_name')}")
        
        # 测试运行状态
        is_running = app.is_running()
        print(f"✅ 运行状态检查: {is_running}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便捷函数"""
    print("=" * 50)
    print("测试便捷函数...")
    
    try:
        from maya_file_manager import create_maya_manager
        from maya_file_manager.config import create_maya_config, create_max_config
        
        # 测试Maya管理器创建
        manager = create_maya_manager("test_project", "D:/test/")
        print("✅ Maya管理器创建成功")
        
        # 测试配置创建函数
        maya_config = create_maya_config("maya_test", "D:/maya/")
        max_config = create_max_config("max_test", "D:/max/")
        print("✅ 配置创建函数正常")
        
        print(f"   Maya配置: {maya_config.get('software')} - {maya_config.get('file_extension')}")
        print(f"   Max配置: {max_config.get('software')} - {max_config.get('file_extension')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试Maya文件管理器MVC架构")
    print("=" * 60)
    
    tests = [
        ("配置模块", test_config_module),
        ("模型模块", test_model_module),
        ("视图模块", test_view_module),
        ("控制器模块", test_controller_module),
        ("主应用", test_main_app),
        ("便捷函数", test_convenience_functions),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！MVC架构重构成功！")
        print("\n📋 重构成果:")
        print("   • Model层: 数据管理和业务逻辑分离")
        print("   • View层: UI界面组件模块化")
        print("   • Controller层: 用户交互和协调逻辑")
        print("   • Config层: 配置管理和多软件适配")
        print("   • 观察者模式: 松耦合组件通信")
        print("   • 便捷函数: 简化使用接口")
        print("\n🔧 使用方法:")
        print("   1. 在Maya中运行: exec(open('maya_file_manager_mvc.py').read())")
        print("   2. 导入使用: from maya_file_manager import show_manager")
        print("   3. 自定义配置: 参考examples/usage_examples.py")
    else:
        print("⚠️  部分测试失败，请检查相关模块")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
