@echo off
chcp 65001 >nul
echo ========================================
echo Maya文件管理器 - 卸载脚本
echo ========================================
echo.

:: 设置变量
set "TOOL_NAME=maya_file_manager"
set "SCRIPT_NAME=maya_file_manager_mvc.py"
set "SHELF_SCRIPT=maya_file_manager_shelf.py"

:: 查找Maya版本
echo 🔍 正在查找Maya安装路径...
set "MAYA_FOUND=0"
set "MAYA_VERSIONS=2025 2024 2023 2022"

for %%v in (%MAYA_VERSIONS%) do (
    set "MAYA_DOCS=%USERPROFILE%\Documents\maya\%%v"
    if exist "!MAYA_DOCS!" (
        echo    找到Maya %%v: !MAYA_DOCS!
        set "MAYA_SCRIPTS=!MAYA_DOCS!\scripts"
        set "MAYA_VERSION=%%v"
        set "MAYA_FOUND=1"
        goto :uninstall
    )
)

if "%MAYA_FOUND%"=="0" (
    echo ❌ 未找到Maya安装
    pause
    exit /b 1
)

:uninstall
echo.
echo 🗑️  开始从Maya %MAYA_VERSION%卸载...
echo 目标路径: %MAYA_SCRIPTS%
echo.

:: 删除工具文件夹
if exist "%MAYA_SCRIPTS%\%TOOL_NAME%" (
    echo 🗑️  删除 %TOOL_NAME% 文件夹...
    rmdir /s /q "%MAYA_SCRIPTS%\%TOOL_NAME%"
    if errorlevel 1 (
        echo ❌ 删除 %TOOL_NAME% 文件夹失败
    ) else (
        echo ✅ %TOOL_NAME% 文件夹删除完成
    )
) else (
    echo ⚠️  %TOOL_NAME% 文件夹不存在
)

:: 删除启动脚本
if exist "%MAYA_SCRIPTS%\%SCRIPT_NAME%" (
    echo 🗑️  删除 %SCRIPT_NAME%...
    del "%MAYA_SCRIPTS%\%SCRIPT_NAME%"
    if errorlevel 1 (
        echo ❌ 删除 %SCRIPT_NAME% 失败
    ) else (
        echo ✅ %SCRIPT_NAME% 删除完成
    )
) else (
    echo ⚠️  %SCRIPT_NAME% 不存在
)

:: 删除shelf脚本
if exist "%MAYA_SCRIPTS%\%SHELF_SCRIPT%" (
    echo 🗑️  删除 %SHELF_SCRIPT%...
    del "%MAYA_SCRIPTS%\%SHELF_SCRIPT%"
    if errorlevel 1 (
        echo ❌ 删除 %SHELF_SCRIPT% 失败
    ) else (
        echo ✅ %SHELF_SCRIPT% 删除完成
    )
) else (
    echo ⚠️  %SHELF_SCRIPT% 不存在
)

:: 检查备份文件
echo.
echo 🔍 检查备份文件...
if exist "%MAYA_SCRIPTS%\%TOOL_NAME%_backup" (
    echo ⚠️  发现备份文件夹: %TOOL_NAME%_backup
    echo    如需恢复，请手动重命名为: %TOOL_NAME%
)

if exist "%MAYA_SCRIPTS%\%SCRIPT_NAME%.backup" (
    echo ⚠️  发现备份文件: %SCRIPT_NAME%.backup
    echo    如需恢复，请手动重命名为: %SCRIPT_NAME%
)

echo.
echo ✅ 卸载完成！
echo.
echo 💡 注意：
echo    - 备份文件已保留，可手动恢复
echo    - 如果Maya中有自定义的shelf按钮，请手动删除
echo    - 重启Maya后卸载生效
echo.

pause
