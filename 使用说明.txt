Maya文件管理器 - 使用说明
========================================

📦 安装方法
----------------------------------------
1. 双击运行 "install_to_maya.bat" 文件
2. 脚本会自动检测Maya版本并安装工具
3. 安装完成后重启Maya即可使用

🚀 启动方法
----------------------------------------
方法1 - 脚本编辑器：
在Maya的脚本编辑器中运行：
exec(open('maya_file_manager_mvc.py').read())

方法2 - Python命令：
from maya_file_manager import show_manager
show_manager()

方法3 - 创建Shelf按钮：
1. 在Maya的Shelf上右键创建新按钮
2. 将以下代码粘贴到按钮的Command中：
   exec(open('maya_file_manager_mvc.py').read())

📋 功能说明
----------------------------------------
• 版本选择器：选择要操作的文件版本
• Open按钮：打开选中的版本文件
• Save按钮：保存当前场景到选中版本
• 信息显示区：显示文件状态和路径
• 日志区域：可折叠的操作记录

🔧 控制命令
----------------------------------------
重启工具：show_manager() 或 重启文件管理器()
关闭工具：close_manager() 或 关闭文件管理器()

⚙️ 自定义配置
----------------------------------------
# 修改项目名称和保存路径
from maya_file_manager import create_maya_manager
manager = create_maya_manager("my_project", "D:/my_scenes/")
manager.start()

🗑️ 卸载方法
----------------------------------------
双击运行 "uninstall_from_maya.bat" 文件

💡 注意事项
----------------------------------------
• 支持Maya 2022及以上版本
• 首次使用会在指定目录创建文件夹
• 版本号格式：v001, v002, v003...
• 绿色表示最新版本，红色表示历史版本，白色表示未创建版本

📞 技术支持
----------------------------------------
如有问题，请检查：
1. Maya版本是否支持（2022+）
2. 脚本路径是否正确
3. 是否有文件夹写入权限

作者：MMQ
版本：2.0 MVC架构版
